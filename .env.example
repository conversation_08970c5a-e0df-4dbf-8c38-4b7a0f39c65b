# ====== K8s MCP Agent 配置示例 ======
# 专用于 Gemini 2.5 Flash 模型的 Kubernetes MCP Agent
# 遵循十二要素应用方法论，所有配置通过环境变量管理
# 
# 使用说明：
# 1. 复制此文件为 .env
# 2. 填入您的真实配置值
# 3. .env 文件会被 git 自动忽略，确保敏感信息安全

# ====== LLM 提供商配置 ======
# OpenRouter API 密钥 - 用于访问 Gemini 2.5 Flash 模型
# 获取地址: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# OpenRouter API 基础 URL
# 默认: https://openrouter.ai/api/v1
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# LLM 提供商名称 (用于显示和日志)
LLM_PROVIDER_NAME=OpenRouter

# ====== 模型配置 ======
# Gemini 2.5 Flash 模型名称
# 支持的模型: google/gemini-2.5-flash, google/gemini-2.5-flash-thinking
LLM_MODEL_NAME=google/gemini-2.5-flash

# 最大输入上下文长度 (tokens)
# Gemini 2.5 Flash 支持最大 1,048,576 tokens
LLM_MAX_INPUT_CONTEXT=1048576

# 最大输出 tokens 数量
# Gemini 2.5 Flash 支持最大 32,768 tokens
LLM_MAX_OUTPUT_TOKENS=32768

# 请求超时时间 (秒)
# 大上下文处理建议使用较长超时时间
LLM_REQUEST_TIMEOUT=600

# ====== 模型行为配置 ======
# 温度设置 (0.0-1.0)
# 0.0 = 确定性输出，1.0 = 最大随机性
# K8s 运维建议使用 0.0 确保一致性
LLM_TEMPERATURE=0.0

# Top-p 采样参数 (0.0-1.0)
# 控制输出的多样性，较低值产生更集中的输出
LLM_TOP_P=0.05

# 最大重试次数
# API 调用失败时的重试次数
LLM_MAX_RETRIES=5

# 随机种子
# 用于确保输出的可重现性
LLM_SEED=42

# ====== MCP 服务器配置 ======
# K8s MCP 服务器 URL
# 用于连接 Kubernetes MCP 服务器进行集群管理
# 格式: http://host:port/path 或 https://host:port/path
# 示例: http://your-k8s-mcp-server:31455/sse
MCP_SERVER_URL=http://your-k8s-mcp-server:port/sse

# MCP 服务器类型
# 支持的类型: sse (Server-Sent Events)
MCP_SERVER_TYPE=sse

# MCP 服务器名称 (用于配置标识)
MCP_SERVER_NAME=k8s

# ====== 安全配置 ======
# 安全停止序列 (用逗号分隔)
# 防止 LLM 生成危险的命令
LLM_SAFETY_STOP_SEQUENCES=```bash,```sh,```shell,rm -rf,kubectl delete,docker rmi,sudo rm

# ====== 应用配置 ======
# 应用环境
# 可选值: development, production, testing
APP_ENVIRONMENT=production

# 日志级别
# 可选值: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# 是否启用详细日志
VERBOSE_LOGGING=false
