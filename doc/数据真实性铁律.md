# K8s MCP Agent 数据真实性铁律

## 🚫 不可违反的核心原则

### ⚡ Fail Fast 异常处理原则 (新增核心铁律)

#### 🚨 立即抛出异常要求
- **强制要求**: 系统遇到任何异常情况时，必须立即抛出异常并终止相关操作
- **严禁fallback**: 不得进行任何形式的fallback处理、降级服务或"优雅"处理
- **零延迟**: 异常发生后必须在毫秒级别内终止操作
- **向上传播**: 所有异常必须向上传播到最顶层

#### 🚫 禁止异常掩盖
```python
# ❌ 严禁的异常掩盖模式
try:
    result = call_mcp_tool()
except Exception:
    return "默认值"  # 绝对禁止

# ❌ 严禁的异常"修复"
try:
    cluster_info = get_cluster_info()
except Exception:
    cluster_info = {"name": "unknown"}  # 绝对禁止

# ✅ 正确的异常处理
try:
    result = call_mcp_tool()
except Exception as e:
    raise MCPToolError(f"工具调用失败: {e}") from e
```

#### 📍 快速问题定位要求
每个异常必须包含以下详细上下文信息：
```python
class K8sAgentException(Exception):
    def __init__(self, message, context):
        super().__init__(message)
        self.context = {
            "operation": context.get("operation"),           # 具体的失败操作
            "mcp_tool": context.get("mcp_tool"),            # MCP工具名称
            "tool_params": context.get("tool_params"),       # 工具调用参数
            "cluster_name": context.get("cluster_name"),     # 集群名称
            "resource_info": context.get("resource_info"),   # 资源信息
            "timestamp": datetime.utcnow().isoformat(),      # 精确的错误时间戳
            "user_input": context.get("user_input"),         # 用户原始输入
            "agent_step": context.get("agent_step")          # Agent执行步骤
        }
```

#### 🚨 零容忍失败场景
以下情况必须立即失败，不得有任何例外：

1. **MCP服务器连接失败**
   ```python
   # 正确实现
   try:
       client = MCPClient.from_dict(config)
   except Exception as e:
       raise SystemExit(1) from e  # 立即终止程序
   ```

2. **工具调用返回错误**
   ```python
   # 正确实现
   if not tool_result.success:
       raise MCPToolCallError(
           f"工具 {tool_name} 调用失败",
           context={
               "tool_name": tool_name,
               "params": tool_params,
               "error": tool_result.error
           }
       )
   ```

3. **数据验证失败**
   ```python
   # 正确实现
   if not validate_cluster_data(data):
       raise DataValidationError(
           "集群数据验证失败",
           context={
               "data": data,
               "validation_rules": validation_rules,
               "failed_fields": get_failed_fields(data)
           }
       )
   ```

4. **集群访问权限不足**
   ```python
   # 正确实现
   if response.status_code == 403:
       raise PermissionError(
           "集群访问权限不足",
           context={
               "cluster": cluster_name,
               "operation": operation,
               "required_permissions": required_perms
           }
       )
   ```

#### 📋 实施范围
此原则适用于整个K8s MCP Agent系统的所有组件：
- ✅ `main.py` - 主程序入口
- ✅ `src/llm_config.py` - LLM配置模块
- ✅ `test_*.py` - 所有测试脚本
- ✅ 任何新增的功能模块
- ✅ MCP工具调用封装
- ✅ 数据验证逻辑

### 绝对禁止行为

#### ❌ 1. 编造集群数据
- **禁止**: Gemini 2.5 Flash编造任何K8s集群数据
- **禁止**: 基于AI训练知识假设集群状态
- **禁止**: 提供未经MCP工具验证的配置信息
- **禁止**: 使用示例性质的集群信息

#### ❌ 2. 模拟响应
- **禁止**: 返回占位符数据（如 "my-k8s-cluster", "example.com"）
- **禁止**: 基于常见配置进行推测
- **禁止**: 提供"典型"或"通常"的集群配置
- **禁止**: 使用模板化的响应

#### ❌ 3. 知识填补
- **禁止**: 当MCP工具调用失败时用AI知识填补空白
- **禁止**: 基于历史经验推断当前状态
- **禁止**: 提供无法验证的故障排查建议
- **禁止**: 在缺乏真实数据时给出运维建议

## ✅ 严格遵循原则

### 1. 数据溯源要求
```
每个数据点 → 必须追溯到 → 具体的MCP工具调用
```

- **要求**: 所有集群信息必须来自K8s MCP工具的真实返回
- **要求**: 保持完整的调用链路记录
- **要求**: 标明数据的获取时间和来源工具
- **要求**: 提供可验证的操作路径

### 2. 实时验证机制
- **要求**: 所有操作基于当前集群的实时状态
- **要求**: 不使用缓存的过期数据
- **要求**: 定期刷新关键信息
- **要求**: 确保信息的时效性

### 3. 透明度原则
- **要求**: 明确标识数据来源
- **要求**: 显示工具调用的详细过程
- **要求**: 区分真实数据和分析建议
- **要求**: 提供数据获取的时间戳

## 🎯 AI职责边界

### Gemini 2.5 Flash 的允许职责

#### ✅ 1. 理解用户意图
```python
# 正确示例
用户输入: "检查集群中有问题的Pod"
AI理解: 需要调用 LIST_CORE_RESOURCES 获取Pod列表，然后分析状态
```

#### ✅ 2. 选择合适的MCP工具
```python
# 正确示例
需求: 获取集群信息
选择: GET_CLUSTER_INFO 工具
参数: {"cluster": "用户指定的集群名"}
```

#### ✅ 3. 基于真实数据提供分析建议
```python
# 正确示例
真实数据: Pod状态为 "CrashLoopBackOff"
AI分析: "根据Pod状态，建议检查容器日志和资源限制"
```

### Gemini 2.5 Flash 的禁止行为

#### ❌ 1. 不得编造集群数据
```python
# 错误示例
❌ "您的集群有3个节点，运行Kubernetes v1.24"  # 未经工具验证
✅ "正在查询集群信息..." → 调用MCP工具 → 返回真实结果
```

#### ❌ 2. 不得假设集群配置
```python
# 错误示例
❌ "通常集群会配置Ingress Controller"  # 基于假设
✅ "让我检查您的集群是否配置了Ingress Controller" → 调用工具查询
```

#### ❌ 3. 不得提供未验证的信息
```python
# 错误示例
❌ "您可能需要增加内存限制"  # 未基于真实数据
✅ 基于真实的资源使用数据 → "根据当前内存使用率85%，建议考虑增加限制"
```

## 🔍 数据验证机制

### 1. 工具调用验证
```python
def validate_tool_call(tool_result):
    """验证MCP工具调用结果"""
    if not tool_result.success:
        raise MCPToolCallError(f"工具调用失败: {tool_result.error}")
    
    if not tool_result.data:
        raise DataEmptyError("工具返回空数据")
    
    return tool_result
```

### 2. 数据完整性检查
```python
def validate_cluster_data(cluster_info):
    """验证集群数据完整性"""
    required_fields = ['name', 'version', 'status']
    
    for field in required_fields:
        if field not in cluster_info:
            return False
    
    return True
```

### 3. 溯源记录
```python
def add_data_provenance(result, tool_name, timestamp):
    """添加数据溯源信息"""
    result.metadata = {
        "source_tool": tool_name,
        "call_timestamp": timestamp,
        "data_type": "real_cluster_data",
        "verification_status": "verified"
    }
    return result
```

## 🚨 失败处理原则 (基于Fail Fast铁律)

### 1. 立即失败原则 (更新)
```python
# ✅ 正确的Fail Fast处理
try:
    cluster_info = call_mcp_tool("GET_CLUSTER_INFO", params)
except MCPError as e:
    # 立即抛出异常，包含详细上下文
    raise K8sAgentException(
        "集群信息获取失败",
        context={
            "operation": "GET_CLUSTER_INFO",
            "mcp_tool": "GET_CLUSTER_INFO",
            "tool_params": params,
            "cluster_name": params.get("cluster"),
            "timestamp": datetime.utcnow().isoformat(),
            "original_error": str(e)
        }
    ) from e
    # ❌ 绝不返回: "集群信息如下: [编造的数据]"
    # ❌ 绝不返回: "❌ 无法获取集群信息，请稍后重试"
```

### 2. 禁止降级处理 (更新)
```python
# ❌ 严禁的"优雅"降级处理
available_tools = check_available_tools()
if "GET_POD_LOGS" not in available_tools:
    return "⚠️ 日志查询功能当前不可用，可以尝试其他诊断方法"  # 禁止

# ✅ 正确的Fail Fast处理
available_tools = check_available_tools()
if "GET_POD_LOGS" not in available_tools:
    raise ToolUnavailableError(
        "关键工具不可用",
        context={
            "missing_tool": "GET_POD_LOGS",
            "available_tools": available_tools,
            "operation": "pod_log_analysis"
        }
    )
```

### 3. 异常分类和立即终止 (更新)
```python
# 定义具体的异常类型，每个都必须立即终止操作
class MCPConnectionError(K8sAgentException):
    """MCP连接失败 - 立即终止程序"""
    pass

class ToolCallError(K8sAgentException):
    """工具调用失败 - 立即终止当前操作"""
    pass

class DataValidationError(K8sAgentException):
    """数据验证失败 - 立即终止并报告"""
    pass

class PermissionError(K8sAgentException):
    """权限不足 - 立即终止并要求检查权限"""
    pass

class ClusterAccessError(K8sAgentException):
    """集群访问失败 - 立即终止并检查连接"""
    pass

# 错误处理映射 - 每个都导致立即失败
ERROR_HANDLING = {
    MCPConnectionError: lambda e: raise SystemExit(1),
    ToolCallError: lambda e: raise e,  # 向上传播
    DataValidationError: lambda e: raise e,  # 向上传播
    PermissionError: lambda e: raise e,  # 向上传播
    ClusterAccessError: lambda e: raise e,  # 向上传播
}
```

### 4. 异常上下文标准化
```python
def create_exception_context(operation, **kwargs):
    """创建标准化的异常上下文"""
    return {
        "operation": operation,
        "timestamp": datetime.utcnow().isoformat(),
        "system_state": get_system_state(),
        "mcp_connection_status": check_mcp_connection(),
        **kwargs
    }

# 使用示例
try:
    result = execute_k8s_operation(params)
except Exception as e:
    context = create_exception_context(
        operation="k8s_resource_query",
        cluster_name=params.get("cluster"),
        resource_type=params.get("resource_type"),
        namespace=params.get("namespace"),
        user_input=original_user_input
    )
    raise K8sAgentException(f"K8s操作失败: {e}", context) from e
```

## 📊 质量保证措施 (包含Fail Fast监控)

### 1. 自动化验证 + Fail Fast监控
- 每次工具调用后自动验证数据完整性
- 检查返回数据的格式和必要字段
- 验证时间戳的合理性
- **新增**: 监控异常抛出的及时性（必须在100ms内抛出）
- **新增**: 检测是否存在异常掩盖行为
- **新增**: 验证异常上下文信息的完整性

### 2. 人工审核检查点 + 异常审核
- 定期审核AI输出的数据来源
- 检查是否存在编造或假设的内容
- 验证分析建议是否基于真实数据
- **新增**: 审核异常处理代码，确保无fallback逻辑
- **新增**: 检查try-catch块是否符合Fail Fast原则
- **新增**: 验证异常信息的详细程度和准确性

### 3. 用户反馈机制 + 异常透明度
- 提供数据来源查询功能
- 允许用户验证信息的准确性
- 收集用户对数据真实性的反馈
- **新增**: 向用户展示详细的异常信息和上下文
- **新增**: 提供异常发生时的系统状态快照
- **新增**: 允许用户报告异常处理不当的情况

### 4. Fail Fast合规性监控 (新增)
```python
class FailFastMonitor:
    """监控Fail Fast原则的执行情况"""

    def __init__(self):
        self.exception_log = []
        self.fallback_violations = []
        self.slow_failures = []

    def log_exception(self, exception, response_time):
        """记录异常及响应时间"""
        if response_time > 0.1:  # 100ms
            self.slow_failures.append({
                "exception": exception,
                "response_time": response_time,
                "timestamp": datetime.utcnow()
            })

    def detect_fallback_violation(self, code_location, behavior):
        """检测fallback违规行为"""
        self.fallback_violations.append({
            "location": code_location,
            "behavior": behavior,
            "timestamp": datetime.utcnow()
        })

    def generate_compliance_report(self):
        """生成Fail Fast合规性报告"""
        return {
            "total_exceptions": len(self.exception_log),
            "slow_failures": len(self.slow_failures),
            "fallback_violations": len(self.fallback_violations),
            "compliance_score": self.calculate_compliance_score()
        }
```

## 🔧 实施要求

### 1. 代码层面
- 所有数据输出必须包含来源标识
- 实现强制的数据验证流程
- 禁用任何可能产生模拟数据的代码路径

### 2. 系统层面
- MCP连接失败时直接终止程序
- 不提供任何形式的"离线模式"
- 确保系统只在有真实数据时运行

### 3. 监控层面
- 监控数据来源的分布
- 检测异常的数据模式
- 记录所有工具调用的成功率

## 📋 违规检测

### 自动检测规则
```python
VIOLATION_PATTERNS = [
    "my-k8s-cluster",      # 通用示例名称
    "example.com",         # 示例域名
    "通常情况下",           # 基于假设的表述
    "一般来说",             # 通用性描述
    "可能是",               # 推测性语言
    "应该是"                # 未验证的断言
]
```

### 人工审核标准
- 检查每个具体的数据点是否有对应的工具调用
- 验证时间戳的一致性
- 确认分析建议基于真实数据

## 🎯 成功标准 (包含Fail Fast指标)

### 数据真实性指标
1. **100%数据真实性**: 所有集群数据都来自真实的MCP工具调用
2. **完整溯源**: 每个数据点都能追溯到具体的工具调用
3. **零编造内容**: 绝不出现任何编造或模拟的集群信息
4. **透明操作**: 用户能够清楚了解数据的来源和获取过程

### Fail Fast执行指标 (新增)
5. **异常响应时间**: 100%的异常必须在100ms内抛出
6. **零fallback行为**: 绝不允许任何形式的降级或fallback处理
7. **异常上下文完整性**: 100%的异常必须包含完整的上下文信息
8. **异常传播率**: 100%的异常必须向上传播，不得被掩盖
9. **立即终止率**: 关键异常（MCP连接失败等）必须100%导致程序立即终止

### 监控和合规指标 (新增)
10. **异常处理合规性**: 所有try-catch块必须100%符合Fail Fast原则
11. **代码审核通过率**: 新增代码必须100%通过Fail Fast原则审核
12. **用户异常可见性**: 用户必须能够看到100%的异常详细信息

### 量化标准
```python
SUCCESS_METRICS = {
    "data_authenticity": 1.0,           # 100%真实数据
    "exception_response_time": 0.1,     # 最大100ms
    "fallback_violations": 0,           # 零fallback
    "exception_context_completeness": 1.0,  # 100%完整上下文
    "exception_propagation_rate": 1.0,  # 100%向上传播
    "critical_failure_termination": 1.0,    # 100%立即终止
    "code_compliance_rate": 1.0,        # 100%代码合规
    "user_exception_visibility": 1.0    # 100%异常可见
}
```

---

**这些原则是不可妥协的系统基石，必须在所有代码、文档和操作中严格执行。**

**版本**: 1.0  
**生效日期**: 2025-06-18  
**适用范围**: 所有K8s MCP Agent相关组件
