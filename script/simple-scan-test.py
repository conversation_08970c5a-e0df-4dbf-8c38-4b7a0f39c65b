#!/usr/bin/env python3
"""
简单的扫描测试脚本
使用最小依赖测试扫描功能
"""

import os
import sys
import asyncio
from pathlib import Path

# 设置Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

def test_environment():
    """测试环境配置"""
    print("🔧 测试环境配置...")
    
    # 加载环境变量
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ .env 文件加载成功")
    except Exception as e:
        print(f"⚠️ .env 文件加载失败: {e}")
    
    # 检查必需变量
    required_vars = ['MCP_SERVER_URL', 'MCP_SERVER_TYPE', 'MCP_SERVER_NAME']
    missing = [var for var in required_vars if not os.getenv(var)]
    
    if missing:
        print(f"❌ 缺少环境变量: {', '.join(missing)}")
        return False
    
    print("✅ 环境变量配置正确")
    for var in required_vars:
        value = os.getenv(var)
        print(f"   {var}: {value}")
    
    return True

def test_imports():
    """测试模块导入"""
    print("\n📦 测试模块导入...")
    
    try:
        # 测试基础依赖
        from mcp_use import MCPClient
        print("✅ mcp_use 导入成功")
        
        # 测试项目模块
        from src.cache import CacheManager
        print("✅ CacheManager 导入成功")
        
        from src.scanner.resource_parser import ResourceParser
        print("✅ ResourceParser 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_cache_manager():
    """测试缓存管理器"""
    print("\n💾 测试缓存管理器...")
    
    try:
        from src.cache import CacheManager
        
        cache_manager = CacheManager()
        print("✅ 缓存管理器创建成功")
        
        # 测试数据库连接
        stats = cache_manager.get_cache_stats()
        print(f"✅ 数据库连接正常，包含 {len(stats)} 个表")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存管理器测试失败: {e}")
        return False

def test_resource_parser():
    """测试资源解析器"""
    print("\n🔧 测试资源解析器...")
    
    try:
        from src.scanner.resource_parser import ResourceParser
        
        parser = ResourceParser()
        print("✅ 资源解析器创建成功")
        
        # 测试解析功能
        stats = parser.get_parsing_stats()
        print(f"✅ 解析器状态正常: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 资源解析器测试失败: {e}")
        return False

async def test_mcp_client():
    """测试MCP客户端"""
    print("\n🔗 测试MCP客户端...")
    
    try:
        from mcp_use import MCPClient
        
        # 创建MCP配置
        config = {
            "mcpServers": {
                os.getenv("MCP_SERVER_NAME", "k8s"): {
                    "type": os.getenv("MCP_SERVER_TYPE", "sse"),
                    "url": os.getenv("MCP_SERVER_URL", "")
                }
            }
        }
        
        mcp_client = MCPClient.from_dict(config)
        print("✅ MCP客户端创建成功")
        
        # 注意：这里不进行实际连接测试，避免网络问题
        print("💡 MCP客户端配置正确，实际连接需要MCP服务器运行")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP客户端测试失败: {e}")
        return False

def test_demo_data():
    """测试演示数据解析"""
    print("\n📊 测试演示数据解析...")
    
    try:
        from src.scanner.resource_parser import ResourceParser
        
        parser = ResourceParser()
        
        # 模拟集群数据
        cluster_data = {
            'metadata': {'name': 'test-cluster'},
            'status': {'version': 'v1.28.0'},
            'spec': {'api_server': 'https://test.example.com:6443'}
        }
        
        cluster = parser.parse_cluster_info(cluster_data)
        print(f"✅ 集群解析成功: {cluster.name} (v{cluster.version})")
        
        # 模拟Pod数据
        pod_data = [{
            'metadata': {
                'name': 'test-pod',
                'namespace': 'default',
                'labels': {'app': 'test'}
            },
            'status': {
                'phase': 'Running',
                'hostIP': '**********',
                'podIP': '***********'
            },
            'spec': {
                'nodeName': 'test-node',
                'containers': [{'name': 'test', 'image': 'nginx:1.21'}]
            }
        }]
        
        pods = parser.parse_pods(pod_data, 'test-cluster')
        print(f"✅ Pod解析成功: {len(pods)} 个Pod")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示数据解析失败: {e}")
        return False

async def main():
    """主函数"""
    print("=" * 60)
    print("🧪 K8s集群扫描简单测试")
    print("=" * 60)
    
    # 测试步骤
    tests = [
        ("环境配置", test_environment),
        ("模块导入", test_imports),
        ("缓存管理器", test_cache_manager),
        ("资源解析器", test_resource_parser),
        ("MCP客户端", test_mcp_client),
        ("演示数据", test_demo_data)
    ]
    
    results = {}
    
    for name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results[name] = result
        except Exception as e:
            print(f"❌ {name}测试异常: {e}")
            results[name] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    for name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / len(results)) * 100
    print(f"\n🎯 总体状态: {passed}/{len(results)} 项通过 ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 基础功能测试通过，扫描系统可以正常工作！")
        print("\n💡 下一步操作:")
        print("   1. 确保MCP服务器运行")
        print("   2. 运行完整扫描测试")
        print("   3. 查看数据库内容")
    else:
        print("❌ 基础功能存在问题，请检查配置")
        print("\n🔧 建议操作:")
        print("   1. 检查项目依赖安装")
        print("   2. 验证环境变量配置")
        print("   3. 确认项目结构完整")

if __name__ == '__main__':
    asyncio.run(main())
