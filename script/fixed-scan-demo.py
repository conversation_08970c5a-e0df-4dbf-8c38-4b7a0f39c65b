#!/usr/bin/env python3
"""
修复后的集群扫描演示脚本
使用Agent来调用MCP工具
"""

import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path

# 设置Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 导入组件
from mcp_use import MCPClient
from src.scanner.cluster_scanner import ClusterScanner
from src.scanner.resource_parser import ResourceParser
from src.scanner.scan_coordinator import ScanCoordinator
from src.cache import CacheManager
from src.mcp_tools import MCPToolLoader


async def demo_fixed_scanning():
    """演示修复后的集群扫描功能"""
    print("=" * 60)
    print("🔧 修复后的K8s集群扫描演示")
    print("=" * 60)
    
    try:
        # 1. 验证环境配置
        print("🔧 验证环境配置...")
        required_vars = ['MCP_SERVER_URL', 'MCP_SERVER_TYPE', 'MCP_SERVER_NAME']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
            return
        
        print("✅ 环境配置验证通过")
        for var in required_vars:
            print(f"   {var}: {os.getenv(var)}")
        
        # 2. 创建MCP客户端
        print("\n🔗 创建MCP客户端...")
        config = {
            "mcpServers": {
                os.getenv("MCP_SERVER_NAME", "k8s"): {
                    "type": os.getenv("MCP_SERVER_TYPE", "sse"),
                    "url": os.getenv("MCP_SERVER_URL", "")
                }
            }
        }
        mcp_client = MCPClient.from_dict(config)
        print("✅ MCP客户端创建成功")
        
        # 3. 创建缓存管理器
        print("💾 创建缓存管理器...")
        cache_manager = CacheManager()
        print("✅ 缓存管理器创建成功")
        
        # 4. 创建工具加载器
        print("🛠️ 创建工具加载器...")
        tool_loader = MCPToolLoader(cache_manager)
        print("✅ 工具加载器创建成功")
        
        # 5. 创建修复后的集群扫描器（现在包含Agent）
        print("🔍 创建集群扫描器（包含LLM和Agent）...")
        cluster_scanner = ClusterScanner(
            mcp_client=mcp_client,
            tool_loader=tool_loader,
            timeout=60,
            max_retries=2
        )
        print("✅ 集群扫描器创建成功")
        print(f"   LLM模型: {cluster_scanner.llm.model_name}")
        print(f"   Agent最大步数: {cluster_scanner.agent.max_steps}")
        
        # 6. 创建资源解析器
        print("🔧 创建资源解析器...")
        resource_parser = ResourceParser()
        print("✅ 资源解析器创建成功")
        
        # 7. 创建扫描协调器
        print("📋 创建扫描协调器...")
        scan_coordinator = ScanCoordinator(
            cluster_scanner=cluster_scanner,
            resource_parser=resource_parser,
            cache_manager=cache_manager,
            static_ttl=1800,  # 30分钟
            dynamic_ttl=300   # 5分钟
        )
        print("✅ 扫描协调器创建成功")
        
        # 8. 测试单个工具调用
        print("\n🧪 测试单个MCP工具调用...")
        try:
            # 测试集群信息获取
            cluster_info = await cluster_scanner._call_mcp_tool('k8s_get_cluster_info', {})
            print("✅ 集群信息获取成功")
            print(f"   结果类型: {type(cluster_info)}")
            if isinstance(cluster_info, str):
                print(f"   结果长度: {len(cluster_info)} 字符")
            elif isinstance(cluster_info, dict):
                print(f"   结果字段: {list(cluster_info.keys())}")
            
        except Exception as e:
            print(f"⚠️ 单个工具调用测试失败: {e}")
            print("   这可能是因为MCP服务器连接问题")
        
        # 9. 测试静态资源扫描
        print("\n🔍 测试静态资源扫描...")
        try:
            static_results = await cluster_scanner.scan_static_resources("test-cluster")
            print("✅ 静态资源扫描成功")
            print(f"   扫描结果: {list(static_results.keys())}")
            
            for resource_type, data in static_results.items():
                if isinstance(data, list):
                    print(f"   {resource_type}: {len(data)} 项")
                elif isinstance(data, dict):
                    print(f"   {resource_type}: {len(data)} 字段")
                else:
                    print(f"   {resource_type}: {type(data)}")
                    
        except Exception as e:
            print(f"⚠️ 静态资源扫描失败: {e}")
            print("   这可能是因为MCP服务器连接问题或工具不可用")
        
        # 10. 测试动态资源扫描
        print("\n🔄 测试动态资源扫描...")
        try:
            dynamic_results = await cluster_scanner.scan_dynamic_resources("test-cluster", "default")
            print("✅ 动态资源扫描成功")
            print(f"   扫描结果: {list(dynamic_results.keys())}")
            
            for resource_type, data in dynamic_results.items():
                if isinstance(data, list):
                    print(f"   {resource_type}: {len(data)} 项")
                elif isinstance(data, dict):
                    print(f"   {resource_type}: {len(data)} 字段")
                else:
                    print(f"   {resource_type}: {type(data)}")
                    
        except Exception as e:
            print(f"⚠️ 动态资源扫描失败: {e}")
            print("   这可能是因为MCP服务器连接问题或工具不可用")
        
        # 11. 显示扫描统计
        stats = cluster_scanner.get_scan_stats()
        print(f"\n📊 扫描统计:")
        print(f"   扫描次数: {stats['scan_count']}")
        print(f"   错误次数: {stats['error_count']}")
        print(f"   成功率: {stats['success_rate']:.1f}%")
        print(f"   平均扫描时间: {stats['avg_scan_time']:.2f}秒")
        
        print("\n" + "=" * 60)
        print("✅ 修复后的集群扫描演示完成!")
        print("🎯 关键修复:")
        print("   1. 在ClusterScanner中创建了LLM实例")
        print("   2. 创建了MCPAgent来处理工具调用")
        print("   3. 通过Agent.run()方法调用MCP工具")
        print("   4. 不再直接使用MCPClient调用工具")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🚀 启动修复后的集群扫描演示程序")
    asyncio.run(demo_fixed_scanning())


if __name__ == '__main__':
    main()
