#!/usr/bin/env python3
"""
K8s集群扫描演示启动脚本
修复导入问题的独立启动脚本
"""

import asyncio
import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
src_path = project_root / 'src'
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 导入MCP和扫描组件
from mcp_use import MCPClient

# 使用相对于src的导入
sys.path.insert(0, str(src_path))
from scanner.cluster_scanner import ClusterScanner
from scanner.resource_parser import ResourceParser
from scanner.scan_coordinator import ScanCoordinator
from cache import CacheManager
from mcp_tools import MCPToolLoader


async def demo_cluster_scanning():
    """演示集群扫描功能"""
    print("=" * 60)
    print("🔍 K8s集群扫描器演示")
    print("=" * 60)
    
    try:
        # 1. 验证环境配置
        print("🔧 验证环境配置...")
        required_vars = ['MCP_SERVER_URL', 'MCP_SERVER_TYPE', 'MCP_SERVER_NAME']
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
            print("💡 请检查 .env 文件配置")
            return
        
        print("✅ 环境配置验证通过")
        for var in required_vars:
            print(f"   {var}: {os.getenv(var)}")
        
        # 2. 初始化组件
        print("\n🔧 初始化组件...")
        
        # 创建MCP客户端
        config = {
            "mcpServers": {
                os.getenv("MCP_SERVER_NAME", "k8s"): {
                    "type": os.getenv("MCP_SERVER_TYPE", "sse"),
                    "url": os.getenv("MCP_SERVER_URL", "")
                }
            }
        }
        mcp_client = MCPClient.from_dict(config)
        print("✅ MCP客户端创建成功")
        
        # 创建缓存管理器
        cache_manager = CacheManager()
        print("✅ 缓存管理器创建成功")
        
        # 创建工具加载器
        tool_loader = MCPToolLoader(cache_manager)
        print("✅ 工具加载器创建成功")
        
        # 创建扫描组件
        cluster_scanner = ClusterScanner(
            mcp_client=mcp_client,
            tool_loader=tool_loader,
            timeout=60,
            max_retries=2
        )
        
        resource_parser = ResourceParser()
        
        scan_coordinator = ScanCoordinator(
            cluster_scanner=cluster_scanner,
            resource_parser=resource_parser,
            cache_manager=cache_manager,
            static_ttl=1800,  # 30分钟
            dynamic_ttl=300   # 5分钟
        )
        print("✅ 扫描组件创建成功")
        
        # 3. 预加载MCP工具
        print("\n🛠️ 预加载MCP工具...")
        try:
            tools = await tool_loader.load_tools()
            print(f"✅ 成功加载 {len(tools)} 个MCP工具")
            
            # 显示可用工具
            for tool in tools[:5]:  # 只显示前5个
                print(f"   - {tool.name}: {tool.description}")
            if len(tools) > 5:
                print(f"   ... 还有 {len(tools) - 5} 个工具")
                
        except Exception as e:
            print(f"⚠️ 工具预加载失败，使用模拟数据: {e}")
        
        # 4. 执行集群扫描演示
        print("\n🔍 执行集群扫描演示...")
        cluster_name = "demo-cluster"
        
        try:
            # 执行完整集群扫描
            scan_result = await scan_coordinator.scan_cluster_full(
                cluster_name=cluster_name,
                include_static=True,
                include_dynamic=True
            )
            
            print("✅ 集群扫描完成!")
            print(f"📊 扫描统计:")
            print(f"   - 集群名称: {scan_result['cluster_name']}")
            print(f"   - 扫描时长: {scan_result.get('scan_duration', 0):.2f}秒")
            
            # 显示静态资源统计
            static_stats = scan_result.get('static_resources', {})
            if static_stats.get('success'):
                static_data = static_stats.get('data', {})
                print(f"   - 静态资源: {sum(static_data.values())} 个")
                for resource_type, count in static_data.items():
                    print(f"     * {resource_type}: {count}")
            
            # 显示动态资源统计
            dynamic_stats = scan_result.get('dynamic_resources', {})
            if dynamic_stats.get('success'):
                dynamic_data = dynamic_stats.get('data', {})
                print(f"   - 动态资源: {sum(dynamic_data.values())} 个")
                for resource_type, count in dynamic_data.items():
                    print(f"     * {resource_type}: {count}")
            
            # 显示总体统计
            overall_stats = scan_result.get('statistics', {})
            print(f"   - 总资源数: {overall_stats.get('total_resources', 0)}")
            
        except Exception as e:
            print(f"❌ 集群扫描失败: {e}")
            print("💡 这可能是因为MCP服务器连接问题或工具不可用")
            import traceback
            traceback.print_exc()
        
        # 5. 缓存数据查询演示
        print("\n💾 缓存数据查询演示:")
        try:
            # 查询缓存的集群信息
            clusters = cache_manager.list_records('clusters')
            print(f"📋 缓存的集群: {len(clusters)} 个")
            
            # 查询缓存的命名空间
            namespaces = cache_manager.list_records('namespaces')
            print(f"📋 缓存的命名空间: {len(namespaces)} 个")
            
            # 查询缓存的Pod
            pods = cache_manager.list_records('pods')
            print(f"📋 缓存的Pod: {len(pods)} 个")
            
            # 查询缓存元数据
            metadata_records = cache_manager.list_records('cache_metadata')
            print(f"📋 缓存元数据: {len(metadata_records)} 条")
            
        except Exception as e:
            print(f"⚠️ 缓存查询失败: {e}")
        
        print("\n" + "=" * 60)
        print("✅ 集群扫描器演示完成!")
        print("💡 扫描器已成功实现静态和动态资源的扫描、解析和缓存功能")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def demo_resource_parsing():
    """演示资源解析功能"""
    print("=" * 60)
    print("🔧 K8s资源解析器演示")
    print("=" * 60)
    
    # 创建资源解析器
    parser = ResourceParser()
    
    # 模拟集群数据
    cluster_data = {
        'metadata': {'name': 'production-cluster'},
        'status': {'version': 'v1.28.0'},
        'spec': {'api_server': 'https://prod-k8s.example.com:6443'}
    }
    
    # 模拟命名空间数据
    namespace_data = [
        {
            'metadata': {'name': 'default', 'labels': {'env': 'production'}},
            'status': {'phase': 'Active'}
        },
        {
            'metadata': {'name': 'kube-system', 'labels': {'env': 'system'}},
            'status': {'phase': 'Active'}
        }
    ]
    
    # 模拟Pod数据
    pod_data = [
        {
            'metadata': {
                'name': 'web-app-1',
                'namespace': 'default',
                'labels': {'app': 'web', 'version': 'v1.0'}
            },
            'status': {
                'phase': 'Running',
                'hostIP': '**********',
                'podIP': '***********'
            },
            'spec': {
                'nodeName': 'worker-node-1',
                'containers': [
                    {'name': 'web', 'image': 'nginx:1.21'}
                ]
            }
        }
    ]
    
    print("🔧 解析集群信息...")
    cluster = parser.parse_cluster_info(cluster_data)
    print(f"   ✅ 集群: {cluster.name} (版本: {cluster.version})")
    print(f"      🌐 API服务器: {cluster.api_server}")
    
    print("\n🔧 解析命名空间...")
    namespaces = parser.parse_namespaces(namespace_data, 'production-cluster')
    for ns in namespaces:
        print(f"   ✅ 命名空间: {ns.name} (状态: {ns.status})")
        print(f"      🏷️  标签: {ns.labels}")
    
    print("\n🔧 解析Pod...")
    pods = parser.parse_pods(pod_data, 'production-cluster')
    for pod in pods:
        print(f"   ✅ Pod: {pod.name} (命名空间: {pod.namespace}, 状态: {pod.phase})")
        print(f"      🖥️  节点: {pod.node_name}")
        print(f"      📦 容器: {len(pod.containers)} 个")
    
    # 显示解析统计
    stats = parser.get_parsing_stats()
    print(f"\n📊 解析统计:")
    print(f"   - 解析总数: {stats['parsed_count']}")
    print(f"   - 成功率: {stats['success_rate']:.1f}%")
    
    print("✅ 资源解析演示完成!")


def main():
    """主函数"""
    print("🚀 启动集群扫描器演示程序")
    
    # 运行资源解析演示
    demo_resource_parsing()
    
    print("\n" + "=" * 60)
    
    # 运行集群扫描演示
    asyncio.run(demo_cluster_scanning())


if __name__ == '__main__':
    main()
