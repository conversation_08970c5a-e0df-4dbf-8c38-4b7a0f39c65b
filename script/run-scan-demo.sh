#!/bin/bash
# K8s集群扫描演示启动脚本

set -e

echo "🚀 K8s集群扫描演示启动脚本"
echo "=" * 60

# 检查项目根目录
if [ ! -f "src/scanner/scanner_demo.py" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    echo "💡 当前目录应包含 src/scanner/scanner_demo.py 文件"
    exit 1
fi

# 检查环境文件
if [ ! -f ".env" ]; then
    echo "⚠️  警告: .env 文件不存在"
    echo "💡 请创建 .env 文件并配置必要的环境变量"
    echo ""
    echo "示例配置:"
    echo "MCP_SERVER_URL=stdio:///path/to/k8s-mcp"
    echo "MCP_SERVER_TYPE=stdio"
    echo "MCP_SERVER_NAME=k8s-mcp"
    echo "CACHE_DB_PATH=./data/k8s_cache.db"
    echo ""
    read -p "是否继续运行? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 设置Python路径
export PYTHONPATH="$PWD/src:$PYTHONPATH"

# 创建数据目录
mkdir -p data

echo "🔧 环境准备完成"
echo ""

# 提供选项菜单
echo "请选择要执行的操作:"
echo "1) 运行扫描演示 (推荐)"
echo "2) 验证扫描状态"
echo "3) 查看数据库内容"
echo "4) 运行集成测试"
echo "5) 全部执行"
echo ""
read -p "请输入选项 (1-5): " choice

case $choice in
    1)
        echo "🔍 运行扫描演示..."
        uv run python src/scanner/scanner_demo.py
        ;;
    2)
        echo "🔧 验证扫描状态..."
        uv run python script/verify-scan-status.py
        ;;
    3)
        echo "📊 查看数据库内容..."
        uv run python script/query-cache-db.py
        ;;
    4)
        echo "🧪 运行集成测试..."
        uv run python test/test_scan_integration.py
        ;;
    5)
        echo "🎯 执行全部操作..."
        echo ""
        echo "步骤 1/4: 验证扫描状态"
        uv run python script/verify-scan-status.py
        echo ""
        echo "步骤 2/4: 运行扫描演示"
        uv run python src/scanner/scanner_demo.py
        echo ""
        echo "步骤 3/4: 查看数据库内容"
        uv run python script/query-cache-db.py
        echo ""
        echo "步骤 4/4: 运行集成测试"
        uv run python test/test_scan_integration.py
        ;;
    *)
        echo "❌ 无效选项"
        exit 1
        ;;
esac

echo ""
echo "✅ 操作完成!"
echo ""
echo "💡 后续操作建议:"
echo "   - 查看扫描日志了解详细信息"
echo "   - 使用 sqlite3 ./data/k8s_cache.db 直接查询数据库"
echo "   - 检查 .env 文件确保MCP服务器配置正确"
echo "   - 定期运行验证脚本检查系统状态"
